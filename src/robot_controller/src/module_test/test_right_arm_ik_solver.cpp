#include "robot_controller/right_arm_ik_solver.h"
#include <ros/ros.h>
#include <ros/package.h>
#include <iostream>

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "test_right_arm_ik_solver");
    ros::NodeHandle nh;
    
    std::cout << "=== 测试右臂逆运动学求解器类 ===" << std::endl;
    
    // 获取URDF文件路径
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    
    // 创建右臂逆运动学求解器
    RightArmIKSolver ik_solver(path_urdf, "r_Link7");
    
    if (!ik_solver.isInitialized()) {
        std::cerr << "逆运动学求解器初始化失败" << std::endl;
        return -1;
    }
    
    std::cout << "右臂关节数量: " << ik_solver.getJointCount() << std::endl;
    
    // 测试1：正运动学测试
    std::cout << "\n=== 测试1：正运动学 ===" << std::endl;
    Eigen::VectorXd q_test(7);
    q_test << 0.308, 0.688, 0.295, 0.457, 0.161, 0.326, 0.598;
    
    std::cout << "测试关节角度: " << q_test.transpose() << std::endl;
    
    pinocchio::SE3 fk_result = ik_solver.computeForwardKinematics(q_test);
    std::cout << "正运动学结果:" << std::endl;
    std::cout << "位置: " << fk_result.translation().transpose() << std::endl;
    std::cout << "旋转矩阵:\n" << fk_result.rotation() << std::endl;
    
    // 测试2：逆运动学测试（使用正运动学结果作为目标）
    std::cout << "\n=== 测试2：逆运动学（已知解） ===" << std::endl;
    
    Eigen::VectorXd q_init = Eigen::VectorXd::Zero(7);  // 零初始值
    Eigen::VectorXd q_solution;
    
    bool success = ik_solver.solveIK(fk_result, q_init, q_solution);
    
    if (success) {
        std::cout << "逆运动学求解成功！" << std::endl;
        std::cout << "原始关节角度: " << q_test.transpose() << std::endl;
        std::cout << "求解关节角度: " << q_solution.transpose() << std::endl;
        
        // 验证解的准确性
        pinocchio::SE3 verify_pose = ik_solver.computeForwardKinematics(q_solution);
        Eigen::Vector3d pos_error = fk_result.translation() - verify_pose.translation();
        std::cout << "位置误差: " << pos_error.transpose() << " (范数: " << pos_error.norm() << ")" << std::endl;
        
    } else {
        std::cout << "逆运动学求解失败" << std::endl;
    }
    
    // 测试3：新目标位姿的逆运动学
    std::cout << "\n=== 测试3：逆运动学（新目标） ===" << std::endl;
    
    pinocchio::SE3 new_target;
    new_target.translation() << 0.5, -0.2, 0.8;
    new_target.rotation() = Eigen::Matrix3d::Identity();
    
    std::cout << "新目标位姿:" << std::endl;
    std::cout << "位置: " << new_target.translation().transpose() << std::endl;
    std::cout << "旋转矩阵:\n" << new_target.rotation() << std::endl;
    
    Eigen::VectorXd q_new_init = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd q_new_solution;
    
    bool new_success = ik_solver.solveIK(new_target, q_new_init, q_new_solution);
    
    if (new_success) {
        std::cout << "新目标逆运动学求解成功！" << std::endl;
        std::cout << "求解关节角度: " << q_new_solution.transpose() << std::endl;
        
        // 验证解的准确性
        pinocchio::SE3 new_verify_pose = ik_solver.computeForwardKinematics(q_new_solution);
        Eigen::Vector3d new_pos_error = new_target.translation() - new_verify_pose.translation();
        std::cout << "验证位姿:" << std::endl;
        std::cout << "位置: " << new_verify_pose.translation().transpose() << std::endl;
        std::cout << "位置误差: " << new_pos_error.transpose() << " (范数: " << new_pos_error.norm() << ")" << std::endl;
        
    } else {
        std::cout << "新目标逆运动学求解失败" << std::endl;
    }
    
    // 测试4：多个目标位姿的批量测试
    std::cout << "\n=== 测试4：批量测试 ===" << std::endl;
    
    std::vector<Eigen::Vector3d> target_positions = {
        Eigen::Vector3d(0.4, -0.1, 0.9),
        Eigen::Vector3d(0.6, -0.3, 0.7),
        Eigen::Vector3d(0.3, -0.2, 1.0),
        Eigen::Vector3d(0.5, -0.15, 0.85)
    };
    
    int success_count = 0;
    for (size_t i = 0; i < target_positions.size(); ++i) {
        pinocchio::SE3 batch_target;
        batch_target.translation() = target_positions[i];
        batch_target.rotation() = Eigen::Matrix3d::Identity();
        
        Eigen::VectorXd q_batch_init = Eigen::VectorXd::Zero(7);
        Eigen::VectorXd q_batch_solution;
        
        bool batch_success = ik_solver.solveIK(batch_target, q_batch_init, q_batch_solution, 300);
        
        if (batch_success) {
            success_count++;
            pinocchio::SE3 batch_verify = ik_solver.computeForwardKinematics(q_batch_solution);
            Eigen::Vector3d batch_error = batch_target.translation() - batch_verify.translation();
            
            std::cout << "目标 " << (i+1) << ": 成功, 位置误差范数 = " << batch_error.norm() << std::endl;
        } else {
            std::cout << "目标 " << (i+1) << ": 失败" << std::endl;
        }
    }
    
    std::cout << "批量测试结果: " << success_count << "/" << target_positions.size() << " 成功" << std::endl;
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    
    return 0;
}
