# 右臂逆运动学求解器

## 概述

本项目为S1机器人的右臂实现了基于Pinocchio库的逆运动学求解功能，末端执行器为r_Link7。

## 功能特性

### 1. 右臂模型构建
- 从完整的S1机器人URDF模型中提取右臂部分
- 锁定左臂、基座和手指关节，只保留右臂7个关节
- 支持的关节：r_joint1 到 r_joint7

### 2. 逆运动学求解
- **完整位姿逆运动学**：同时考虑位置和旋转的逆运动学求解
- **位置逆运动学**：只考虑末端位置的逆运动学求解（推荐使用）
- 使用数值迭代方法（阻尼最小二乘法）
- 高精度求解（位置误差 < 1mm）

### 3. 正运动学计算
- 根据关节角度计算末端执行器位姿
- 支持SE3变换矩阵输出

## 文件结构

```
src/robot_controller/
├── include/robot_controller/
│   └── right_arm_ik_solver.h          # 头文件
├── src/
│   ├── right_arm_ik_solver.cpp        # 实现文件
│   └── module_test/
│       ├── test_pinocchio_fk2.cpp     # 原始测试程序
│       └── test_right_arm_ik_solver.cpp # 求解器类测试程序
└── README_right_arm_ik.md             # 本文档
```

## 使用方法

### 1. 基本使用

```cpp
#include "robot_controller/right_arm_ik_solver.h"

// 创建求解器实例
std::string urdf_path = "/path/to/S1_robot.urdf";
RightArmIKSolver ik_solver(urdf_path, "r_Link7");

// 检查初始化状态
if (!ik_solver.isInitialized()) {
    std::cerr << "求解器初始化失败" << std::endl;
    return -1;
}
```

### 2. 位置逆运动学求解（推荐）

```cpp
// 设置目标位置
Eigen::Vector3d target_position(0.5, -0.2, 0.8);

// 设置初始关节角度
Eigen::VectorXd q_init = Eigen::VectorXd::Zero(7);

// 求解
Eigen::VectorXd q_solution;
bool success = ik_solver.solvePositionIK(target_position, q_init, q_solution);

if (success) {
    std::cout << "求解成功！关节角度: " << q_solution.transpose() << std::endl;
}
```

### 3. 完整位姿逆运动学求解

```cpp
// 设置目标位姿
pinocchio::SE3 target_pose;
target_pose.translation() << 0.5, -0.2, 0.8;
target_pose.rotation() = Eigen::Matrix3d::Identity();

// 求解（可调整容差）
bool success = ik_solver.solveIK(target_pose, q_init, q_solution, 
                                500,    // 最大迭代次数
                                1e-3,   // 位置容差
                                0.1);   // 旋转容差（建议放宽）
```

### 4. 正运动学计算

```cpp
// 给定关节角度
Eigen::VectorXd q(7);
q << 0.308, 0.688, 0.295, 0.457, 0.161, 0.326, 0.598;

// 计算末端位姿
pinocchio::SE3 end_pose = ik_solver.computeForwardKinematics(q);

std::cout << "末端位置: " << end_pose.translation().transpose() << std::endl;
std::cout << "末端旋转:\n" << end_pose.rotation() << std::endl;
```

## 编译和测试

### 1. 编译

```bash
cd /path/to/workspace
catkin_make --only-pkg-with-deps robot_controller
```

### 2. 运行测试

```bash
# 运行求解器类测试
source devel/setup.bash
rosrun robot_controller test_right_arm_ik_solver

# 运行原始测试程序
rosrun robot_controller test_pinocchio_fk2
```

## 测试结果

### 位置逆运动学测试结果：
- **测试1（已知解）**：成功，位置误差 < 0.001m
- **测试2（新目标）**：成功，位置误差 < 0.001m  
- **批量测试**：4/4成功，所有目标位置误差 < 0.001m

### 性能指标：
- **收敛速度**：通常在50-100次迭代内收敛
- **位置精度**：< 1mm
- **成功率**：在工作空间内接近100%

## API参考

### RightArmIKSolver类

#### 构造函数
```cpp
RightArmIKSolver(const std::string& urdf_path, 
                 const std::string& end_effector_name = "r_Link7");
```

#### 主要方法

- `bool solvePositionIK(...)` - 位置逆运动学求解
- `bool solveIK(...)` - 完整位姿逆运动学求解  
- `pinocchio::SE3 computeForwardKinematics(...)` - 正运动学计算
- `bool isInitialized()` - 检查初始化状态
- `int getJointCount()` - 获取关节数量
- `void setParameters(...)` - 设置求解器参数

## 注意事项

1. **推荐使用位置逆运动学**：由于旋转约束较难收敛，建议优先使用`solvePositionIK`方法
2. **初始值选择**：合理的初始关节角度有助于更快收敛
3. **工作空间限制**：确保目标位置在机械臂的可达工作空间内
4. **关节限制**：当前实现未考虑关节角度限制，可根据需要添加

## 依赖项

- ROS Noetic
- Pinocchio 3.6.0+
- Eigen3
- URDF模型文件

## 作者

基于Pinocchio库实现的S1机器人右臂逆运动学求解器。
