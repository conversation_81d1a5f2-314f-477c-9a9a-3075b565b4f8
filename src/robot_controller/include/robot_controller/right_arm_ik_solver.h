#ifndef RIGHT_ARM_IK_SOLVER_H
#define RIGHT_ARM_IK_SOLVER_H

#include <pinocchio/config.hpp>
#include <pinocchio/parsers/urdf.hpp>
#include <pinocchio/multibody/model.hpp>
#include <pinocchio/multibody/data.hpp>
#include <pinocchio/algorithm/kinematics.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/jacobian.hpp>
#include <pinocchio/algorithm/joint-configuration.hpp>
#include <pinocchio/spatial/explog.hpp>

#include <Eigen/Dense>
#include <vector>
#include <string>

/**
 * @brief 右臂逆运动学求解器类
 * 
 * 该类提供了基于Pinocchio库的右臂逆运动学求解功能，
 * 使用数值迭代方法求解给定末端位姿对应的关节角度。
 */
class RightArmIKSolver {
public:
    /**
     * @brief 构造函数
     * @param urdf_path URDF文件路径
     * @param end_effector_name 末端执行器frame名称，默认为"r_Link7"
     */
    RightArmIKSolver(const std::string& urdf_path, const std::string& end_effector_name = "r_Link7");

    /**
     * @brief 析构函数
     */
    ~RightArmIKSolver();

    /**
     * @brief 求解逆运动学
     * @param target_pose 目标末端位姿（SE3变换）
     * @param q_init 初始关节角度（7维向量）
     * @param q_solution 输出的关节角度解（7维向量）
     * @param max_iterations 最大迭代次数，默认500
     * @param position_tolerance 位置收敛精度，默认1e-3
     * @param rotation_tolerance 旋转收敛精度，默认1e-3
     * @return 是否求解成功
     */
    bool solveIK(const pinocchio::SE3& target_pose,
                 const Eigen::VectorXd& q_init,
                 Eigen::VectorXd& q_solution,
                 int max_iterations = 500,
                 double position_tolerance = 1e-3,
                 double rotation_tolerance = 1e-3);

    /**
     * @brief 求解位置逆运动学（只考虑位置，忽略旋转）
     * @param target_position 目标末端位置（3维向量）
     * @param q_init 初始关节角度（7维向量）
     * @param q_solution 输出的关节角度解（7维向量）
     * @param max_iterations 最大迭代次数，默认500
     * @param position_tolerance 位置收敛精度，默认1e-3
     * @return 是否求解成功
     */
    bool solvePositionIK(const Eigen::Vector3d& target_position,
                        const Eigen::VectorXd& q_init,
                        Eigen::VectorXd& q_solution,
                        int max_iterations = 500,
                        double position_tolerance = 1e-3);

    /**
     * @brief 计算正运动学
     * @param q 关节角度（7维向量）
     * @return 末端执行器位姿
     */
    pinocchio::SE3 computeForwardKinematics(const Eigen::VectorXd& q);

    /**
     * @brief 获取右臂模型信息
     * @return 右臂模型的关节数量
     */
    int getJointCount() const;

    /**
     * @brief 检查求解器是否初始化成功
     * @return 初始化状态
     */
    bool isInitialized() const;

    /**
     * @brief 设置求解器参数
     * @param damping_factor 阻尼系数，默认1e-6
     * @param step_size 步长，默认0.1
     * @param max_step_norm 最大步长范数，默认0.5
     */
    void setParameters(double damping_factor = 1e-6, 
                      double step_size = 0.1, 
                      double max_step_norm = 0.5);

private:
    pinocchio::Model right_arm_model_;          ///< 右臂模型
    pinocchio::Data right_arm_data_;            ///< 右臂数据
    pinocchio::FrameIndex end_effector_id_;     ///< 末端执行器frame ID
    std::string end_effector_name_;             ///< 末端执行器名称
    bool initialized_;                          ///< 初始化状态

    // 求解器参数
    double damping_factor_;                     ///< 阻尼系数
    double step_size_;                          ///< 步长
    double max_step_norm_;                      ///< 最大步长范数

    /**
     * @brief 构建右臂模型（锁定其他关节）
     * @param full_model 完整机器人模型
     * @return 是否构建成功
     */
    bool buildRightArmModel(const pinocchio::Model& full_model);

    /**
     * @brief 获取需要锁定的关节列表
     * @param full_model 完整机器人模型
     * @return 需要锁定的关节ID列表
     */
    std::vector<pinocchio::JointIndex> getJointsToLock(const pinocchio::Model& full_model);
};

#endif // RIGHT_ARM_IK_SOLVER_H
